<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Serinix">
    <meta name="keywords" content="<PERSON>, Assistant, Cha<PERSON><PERSON>, Serin AI">
    <meta http-equiv="X-UA-Compatible" content="IE=7">
    <title>Serinix</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dompurify/2.3.10/purify.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/es6-promise@4/dist/es6-promise.auto.min.js"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />

    <!-- تعطيل AMD مؤقتًا قبل تحميل PopperJS وTippyJS -->
    <script>
      window.__define_backup = window.define;
      window.define = undefined;
    </script>
    <script src="https://unpkg.com/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/tippy.js@6.3.7/dist/tippy.css" />
    <link rel="stylesheet" href="https://unpkg.com/tippy.js@6.3.7/animations/scale.css" />
    <script src="https://unpkg.com/tippy.js@6.3.7/dist/tippy-bundle.umd.min.js"></script>
    <script>
      if (window.__define_backup) window.define = window.__define_backup;
      window.__define_backup = undefined;
    </script>
    <!-- مكتبات إضافية - استخدام نسخ غير AMD لتجنب التعارض -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" />

    <link rel="stylesheet" href="style.css">
    <!-- أنماط زر فتح في المحرر -->
    

</head>

<body>
    <!-- تم إزالة زر تبديل الاتجاه -->

    <div class="file-explorer" id="file-explorer">
        <div class="explorer-header">
            <div class="explorer-title">المستكشف</div>
            <button class="explorer-action" onclick="createNewFile()" title="ملف جديد">
                <i class="fas fa-file"></i>
            </button>
            <button class="explorer-action" onclick="createNewFolder()" title="مجلد جديد">
                <i class="fas fa-folder"></i>
            </button>
            <button class="explorer-action" onclick="uploadFile()" title="رفع ملف">
                <i class="fas fa-upload"></i>
            </button>
        </div>
        <div class="explorer-content" id="explorer-content">
            <!-- سيتم ملؤها بالملفات والمجلدات -->
        </div>
    </div>

    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="sidebar-title">المحادثات</div>
            <button type="button" class="new-chat-btn" onclick="createNewChat()" title="إنشاء محادثة جديدة">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 -12 100 125" preserveAspectRatio="xMidYMid meet" width="20" height="20">
                    <path
                        d="M67.58,30.8a1.4,1.4,0,0,1,.62.62l1.33,2.67a1.64,1.64,0,0,0,2.94,0l1.33-2.67a1.4,1.4,0,0,1,.62-.62l2.67-1.34A1.63,1.63,0,0,0,78,28a1.61,1.61,0,0,0-.91-1.47L74.42,25.2a1.4,1.4,0,0,1-.62-.62l-1.33-2.67a1.64,1.64,0,0,0-2.94,0L68.2,24.58a1.4,1.4,0,0,1-.62.62l-2.67,1.34a1.64,1.64,0,0,0,0,2.93Z"
                        style="fill:#ffffff;"></path>
                    <path
                        d="M79.05,42.85a2.7,2.7,0,0,0-2.53.12h0l-.22.13a3.27,3.27,0,0,0-1.55,3.38,25.53,25.53,0,0,1-1,11.42A25,25,0,0,1,48.23,74.95a22.68,22.68,0,0,1-8.35-2,9,9,0,0,0-7-.35l-6.56,2.38a1,1,0,0,1-1.28-1.28l2.38-6.56A9.19,9.19,0,0,0,27.06,60,23.57,23.57,0,0,1,25,50,25,25,0,0,1,50,25c.77,0,1.54,0,2.31.11a3.23,3.23,0,0,0,3.22-1.67l.11-.23a2.84,2.84,0,0,0,.15-2.28A31,31,0,0,0,19,50a29.18,29.18,0,0,0,3,13.44l.2.39L19.5,71.4A7.26,7.26,0,0,0,20,77.57a7,7,0,0,0,8.42,3l7.78-2.83,.39.2A29.19,29.19,0,0,0,50,81A31,31,0,0,0,80.59,45,2.85,2.85,0,0,0,79.05,42.85Z"
                        style="fill:#ffffff"></path>
                </svg>
            </button>
        </div>
        <div class="conversations-list" id="conversations-list"></div>
    </div>
    
    <div class="container" id="main-content">
        <div class="header">
            <span class="head">Serinix</span>
            <div class="header-buttons">
                <button type="button" class="header-button" id="explorer-toggle" onclick="toggleExplorerToolbar()" title="فتح مستكشف الملفات">
                    <i class="fas fa-folder-open"></i>
                </button>
                <button type="button" class="header-button" id="menu-toggle" onclick="toggleSidebarToolbar()" title="فتح القائمة الجانبية">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="header-button-group">
                    <button type="button" class="header-button" id="notifications-toggle" onclick="toggleNotificationMenu()" title="إعدادات الإشعارات">
                        <i class="fas fa-bell"></i>
                    </button>
                    <div class="notification-menu" id="notification-menu" style="display: none;">
                        <div class="notification-menu-item" onclick="toggleAllNotificationsFromMenu()">
                            <i class="fas fa-bell-slash"></i>
                            <span id="toggle-all-text">تعطيل جميع الإشعارات</span>
                        </div>
                        <div class="notification-menu-item" onclick="showNotificationSettings()">
                            <i class="fas fa-cog"></i>
                            <span>إعدادات متقدمة</span>
                        </div>
                        <div class="notification-menu-item" onclick="clearAllNotifications()">
                            <i class="fas fa-trash"></i>
                            <span>مسح جميع الإشعارات</span>
                        </div>
                    </div>
                </div>
                <button type="button" class="header-button" id="pluginbar-toggle" onclick="togglePluginbarToolbar()" title="الإضافات">
                    <i class="fas fa-plug"></i>
                </button>
            </div>
        </div>
        <div class="chat-window" id="chat-window">
            <!-- شاشة الترحيب الأولية -->
            <div class="welcome-screen" id="welcome-screen">
                <div class="welcome-content">
                    <div class="welcome-logo">
                        <div class="logo-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h1 class="logo-text">Serinix</h1>
                    </div>
                    <div class="welcome-message">
                        <h2>مرحباً بك في Serinix</h2>
                        <p>مساعدك الذكي للبرمجة والتطوير</p>
                    </div>
                    <div class="welcome-suggestions">
                        <div class="suggestion-item" onclick="insertSuggestion('مرحباً، أريد إنشاء موقع ويب جديد')">
                            <i class="fas fa-globe"></i>
                            <span>إنشاء موقع ويب</span>
                        </div>
                        <div class="suggestion-item" onclick="insertSuggestion('أريد تعلم JavaScript من البداية')">
                            <i class="fab fa-js-square"></i>
                            <span>تعلم JavaScript</span>
                        </div>
                        <div class="suggestion-item" onclick="insertSuggestion('ساعدني في حل مشكلة في الكود')">
                            <i class="fas fa-bug"></i>
                            <span>حل مشاكل الكود</span>
                        </div>
                        <div class="suggestion-item" onclick="insertSuggestion('أريد تحسين أداء موقعي')">
                            <i class="fas fa-rocket"></i>
                            <span>تحسين الأداء</span>
                        </div>
                    </div>
                    <div class="welcome-footer">
                        <p>ابدأ بكتابة رسالتك أدناه أو اختر أحد الاقتراحات</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer">
            <textarea rows="1" id="chat-input" placeholder="اكتب رسالتك هنا..." onkeydown="handleKeyDown(event)"></textarea>
            <button type="button" onclick="sendMessage()" title="إرسال الرسالة">
                <svg width="14" height="16" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M7 16c-.595 0-1.077-.462-1.077-1.032V1.032C5.923.462 6.405 0 7 0s1.077.462 1.077 1.032v13.936C8.077 15.538 7.595 16 7 16z"
                        fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M.315 7.44a1.002 1.002 0 0 1 0-1.46L6.238.302a1.11 1.11 0 0 1 1.523 0c.421.403.421 1.057 0 1.46L1.838 7.44a1.11 1.11 0 0 1-1.523 0z"
                        fill="currentColor"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M13.685 7.44a1.11 1.11 0 0 1-1.523 0L6.238 1.762a1.002 1.002 0 0 1 0-1.46 1.11 1.11 0 0 1 1.523 0l5.924 5.678c.42.403.42 1.056 0 1.46z"
                        fill="currentColor"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- واجهة تشغيل الأكواد -->
    <div class="code-executor" id="code-executor">
        <div class="executor-header">
            <div class="file-tabs" id="file-tabs">
                <!-- سيتم إضافة علامات تبويب الملفات هنا -->
            </div>
            <div class="executor-actions">
                <button class="executor-btn executor-run" title="تشغيل (F5)" onclick="runCodeInExecutor()">
                    <i class="fas fa-play"></i>
                </button>
                <button class="executor-btn executor-clear" title="إغلاق (ESC)" onclick="hideCodeExecutor()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="editor-container" id="editor-container"></div>
        <div class="executor-footer">
            <div class="terminal-header">
                <span>النتائج</span>
                <button onclick="document.getElementById('executor-result').textContent = ''">مسح</button>
            </div>
            <div class="terminal" id="executor-result"></div>
        </div>
    </div>

    <!-- عنصر رفع الملفات المخفي -->
    <input type="file" id="file-upload" style="display: none;" multiple onchange="handleFileUpload(event)">

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/prism.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-python.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-java.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/line-numbers/prism-line-numbers.min.js"></script>

    <!-- Mermaid Library -->
    <script src="https://unpkg.com/mermaid@10.6.1/dist/mermaid.min.js"></script>

    <!-- jsPDF Library for PDF export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script>
        // تهيئة Mermaid فور تحميلها
        if (typeof mermaid !== 'undefined') {
            mermaid.initialize({
                startOnLoad: false,
                theme: 'dark',
                themeVariables: {
                    primaryColor: '#3b82f6',
                    primaryTextColor: '#ffffff',
                    primaryBorderColor: '#1d4ed8',
                    lineColor: '#6b7280',
                    sectionBkgColor: '#374151',
                    altSectionBkgColor: '#4b5563',
                    gridColor: '#6b7280',
                    secondaryColor: '#10b981',
                    tertiaryColor: '#f59e0b'
                }
            });
        }
    </script>

    <script src="script.js"></script>


    <div class="pluginbar" id="pluginbar">
        <div class="pluginbar-header">
            <div class="pluginbar-title">الإضافات (Plugins)</div>
            <button class="explorer-action" onclick="togglePluginbarToolbar()" title="إغلاق الإضافات">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="pluginbar-list" id="pluginbar-list"></div>
    </div>

    <!-- Web Preview Sidebar -->
    <div class="web-preview-sidebar" id="web-preview-sidebar" style="display: none;">
        <div class="web-preview-header">
            <div class="preview-drag-handle">
                <i class="fas fa-grip-lines"></i>
                <span>HTML Preview</span>
            </div>
            <div class="preview-controls">
                <button class="preview-btn" id="preview-refresh" title="تحديث"><i class="fas fa-sync-alt"></i></button>
                <div class="device-select-wrapper">
                    <select id="device-selector" title="اختيار الجهاز">
                        <option value="responsive">تجاوب كامل</option>
                        <option value="desktop">سطح المكتب (1920×1080)</option>
                        <option value="laptop">لابتوب (1366×768)</option>
                        <option value="tablet">تابلت (768×1024)</option>
                        <option value="mobile">موبايل (375×667)</option>
                        <option value="custom">مخصص...</option>
                    </select>
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <button class="preview-btn" id="preview-minimize" title="تصغير"><i class="fas fa-window-minimize"></i></button>
                <button class="preview-btn" id="preview-maximize" title="تكبير"><i class="fas fa-window-maximize"></i></button>
                <button class="preview-btn preview-close" id="preview-close" onclick="closeWebPreview()" title="إغلاق"><i class="fas fa-times"></i></button>
            </div>
        </div>
        
        <!-- شريط أدوات المفتش (مشابه لمفتش المتصفح) -->
        <div class="inspector-toolbar" id="inspector-toolbar">
            <div class="inspector-tools">
                <button class="inspector-btn" id="inspector-cursor" title="مؤشر عادي" data-active="true"><i class="fas fa-mouse-pointer"></i></button>
                <button class="inspector-btn" id="inspector-inspect" title="فحص العناصر"><i class="fas fa-search"></i></button>
                <button class="inspector-btn" id="inspector-responsive" title="وضع التجاوب"><i class="fas fa-tablet-alt"></i></button>
                <button class="inspector-btn" id="inspector-ruler" title="قياس الأبعاد"><i class="fas fa-ruler-combined"></i></button>
                <span class="inspector-separator"></span>
                <button class="inspector-btn" id="inspector-toggle-grid" title="إظهار شبكة"><i class="fas fa-th"></i></button>
                <button class="inspector-btn" id="inspector-toggle-outline" title="إظهار حدود العناصر"><i class="fas fa-border-all"></i></button>
            </div>
            <div class="device-size">
                <span id="device-width">0</span> × <span id="device-height">0</span>
            </div>
            <div class="inspector-device-controls">
                <button class="inspector-btn" id="rotate-device" title="تدوير الجهاز"><i class="fas fa-sync-alt"></i></button>
                <select id="device-zoom" title="تكبير/تصغير">
                    <option value="0.5">50%</option>
                    <option value="0.75">75%</option>
                    <option value="1" selected>100%</option>
                    <option value="1.25">125%</option>
                    <option value="1.5">150%</option>
                </select>
                <div class="network-select">
                    <select id="network-throttle" title="محاكاة سرعة الشبكة">
                        <option value="online">شبكة عادية</option>
                        <option value="fast3g">3G سريع</option>
                        <option value="slow3g">3G بطيء</option>
                        <option value="offline">غير متصل</option>
                    </select>
                    <i class="fas fa-wifi"></i>
                </div>
            </div>
        </div>
        
        <div class="browser-controls">
            <div class="browser-actions">
                <button class="browser-btn" title="رجوع"><i class="fas fa-arrow-left"></i></button>
                <button class="browser-btn" title="إعادة تحميل"><i class="fas fa-redo"></i></button>
                <div class="browser-address-bar">
                    <i class="fas fa-lock"></i>
                    <span>localhost</span>
                </div>
            </div>
        </div>
        <div class="preview-container" id="preview-container">
            <div class="device-wrapper">
                <div class="device-frame" id="device-frame">
                    <iframe id="web-preview-iframe"></iframe>
                </div>
            </div>
            <div class="preview-loading" id="preview-loading">
                <div class="preview-loading-spinner"></div>
            </div>
            
            <!-- طبقات المفتش -->
            <div class="inspector-overlay" id="inspector-overlay" style="display: none;">
                <div class="element-highlighter" id="element-highlighter"></div>
                <div class="element-info" id="element-info"></div>
                <div class="measurement-overlay" id="measurement-overlay"></div>
                <div class="grid-overlay" id="grid-overlay" style="display: none;"></div>
            </div>
        </div>
        <div class="preview-resize-handle" id="preview-resize-handle"></div>
        
        <!-- شريط المعلومات في أسفل المفتش -->
        <div class="inspector-status-bar" id="inspector-status-bar">
            <div class="element-path" id="element-path"></div>
            <div class="style-info">
                <span id="selected-element-size"></span>
                <span class="status-separator">|</span>
                <span id="mouse-position"></span>
            </div>
        </div>
    </div>



    <!-- نظام الإشعارات التفاعلية -->
    <script src="js/notifications.js"></script>

    <!-- نظام حقول الإدخال التفاعلية -->
    <script src="js/interactive-inputs.js"></script>

    <!-- إزالة تحميل Monaco Editor المكرر -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.34.0/min/vs/loader.min.js"></script>
    <script>
      // تحميل المحرر مرة واحدة فقط
      require.config({
        paths: {
          vs: 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.34.0/min/vs',
          'source-map': { load: function() { return {}; } }
        }
      });
      
      require(['vs/editor/editor.main'], function() {
        console.log('Monaco Editor تم تحميله بنجاح');
      });
    </script>
</body>
</html>