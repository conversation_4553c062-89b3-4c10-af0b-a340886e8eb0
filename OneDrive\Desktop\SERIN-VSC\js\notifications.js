/**
 * نظام الإشعارات التفاعلية المتطور
 * يوفر إشعارات منبثقة ونوافذ حوار تفاعلية مع إمكانية التحكم في تفعيلها/تعطيلها
 */

class NotificationSystem {
    constructor() {
        this.notifications = new Map();
        this.notificationId = 0;
        this.containers = new Map();
        this.currentPosition = 'top-right';

        // إعدادات التحكم في الإشعارات
        this.settings = this.loadSettings();

        this.init();
    }

    /**
     * تحميل إعدادات الإشعارات من localStorage
     */
    loadSettings() {
        const defaultSettings = {
            enabled: true,
            types: {
                success: false, // إشعارات النجاح معطلة افتراضياً
                error: true,
                warning: true,
                info: true,
                confirm: true,
                progress: true
            },
            sound: true,
            position: 'top-right',
            duration: 5000
        };

        try {
            const saved = localStorage.getItem('notificationSettings');
            return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
        } catch (error) {
            console.warn('خطأ في تحميل إعدادات الإشعارات:', error);
            return defaultSettings;
        }
    }

    /**
     * حفظ إعدادات الإشعارات في localStorage
     */
    saveSettings() {
        try {
            localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
        } catch (error) {
            console.warn('خطأ في حفظ إعدادات الإشعارات:', error);
        }
    }

    /**
     * تحديث إعدادات الإشعارات
     */
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveSettings();

        // تحديث الموضع إذا تغير
        if (newSettings.position && newSettings.position !== this.currentPosition) {
            this.setPosition(newSettings.position);
        }
    }

    /**
     * التحقق من إمكانية عرض نوع معين من الإشعارات
     */
    canShowNotification(type) {
        return this.settings.enabled && this.settings.types[type];
    }

    init() {
        // إنشاء حاويات الإشعارات للمواضع المختلفة
        this.createContainers();

        // إضافة أصوات الإشعارات (اختيارية)
        this.sounds = {
            success: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            error: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            warning: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'),
            info: new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT')
        };
    }

    createContainers() {
        const positions = ['top-right', 'top-left', 'bottom-right', 'bottom-left', 'top-center', 'bottom-center'];

        positions.forEach(position => {
            const container = document.createElement('div');
            container.className = `notification-container notification-${position}`;
            container.style.display = position === this.currentPosition ? 'block' : 'none';
            document.body.appendChild(container);
            this.containers.set(position, container);
        });

        // تعيين الحاوية الافتراضية
        this.container = this.containers.get(this.currentPosition);
    }

    setPosition(position) {
        // إخفاء الحاوية الحالية
        if (this.container) {
            this.container.style.display = 'none';
        }

        // تعيين الموضع الجديد
        this.currentPosition = position;
        this.container = this.containers.get(position);

        if (this.container) {
            this.container.style.display = 'block';
        }
    }

    /**
     * عرض إشعار منبثق
     */
    showNotification(options) {
        const {
            type = 'info',
            title = '',
            message = '',
            duration = this.settings.duration,
            actions = [],
            sound = this.settings.sound,
            progress = false
        } = options;

        // التحقق من إمكانية عرض هذا النوع من الإشعارات
        if (!this.canShowNotification(type)) {
            return null; // لا تعرض الإشعار إذا كان معطلاً
        }

        const id = ++this.notificationId;
        const notification = this.createNotificationElement(id, type, title, message, actions, progress);

        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // تشغيل الصوت
        if (sound && this.sounds[type]) {
            this.sounds[type].play().catch(() => {});
        }

        // إظهار الإشعار مع تأثير انتقالي
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });

        // إخفاء تلقائي
        if (duration > 0) {
            if (progress) {
                this.startProgress(notification, duration);
            }
            setTimeout(() => {
                this.hideNotification(id);
            }, duration);
        }

        return id;
    }

    /**
     * إنشاء عنصر الإشعار
     */
    createNotificationElement(id, type, title, message, actions, progress) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.dataset.id = id;

        const icons = {
            success: '✓',
            error: '✕',
            warning: '⚠',
            info: 'ℹ'
        };

        const titles = {
            success: 'نجح',
            error: 'خطأ',
            warning: 'تحذير',
            info: 'معلومات'
        };

        const emojis = {
            success: '🎉',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        notification.innerHTML = `
            <div class="notification-header">
                <div class="notification-title">
                    <div class="notification-icon">${icons[type]}</div>
                    ${emojis[type]} ${title || titles[type]}
                </div>
                <button class="notification-close" onclick="notificationSystem.hideNotification(${id})">×</button>
            </div>
            ${message ? `<div class="notification-content">${message}</div>` : ''}
            ${actions.length > 0 ? this.createActionsHTML(actions, id) : ''}
            ${progress ? '<div class="notification-progress" style="width: 100%"></div>' : ''}
        `;

        return notification;
    }

    /**
     * إنشاء HTML للأزرار
     */
    createActionsHTML(actions, notificationId) {
        const actionsHTML = actions.map(action => {
            const className = `notification-btn ${action.primary ? 'primary' : ''}`;
            return `<button class="${className}" onclick="notificationSystem.handleAction(${notificationId}, '${action.id}')">${action.text}</button>`;
        }).join('');

        return `<div class="notification-actions">${actionsHTML}</div>`;
    }

    /**
     * بدء شريط التقدم
     */
    startProgress(notification, duration) {
        const progressBar = notification.querySelector('.notification-progress');
        if (!progressBar) return;

        let width = 100;
        const interval = 50;
        const decrement = (100 / duration) * interval;

        const timer = setInterval(() => {
            width -= decrement;
            if (width <= 0) {
                clearInterval(timer);
                width = 0;
            }
            progressBar.style.width = width + '%';
        }, interval);
    }

    /**
     * إخفاء إشعار
     */
    hideNotification(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        notification.classList.add('hide');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 400);
    }

    /**
     * معالجة إجراءات الأزرار
     */
    handleAction(notificationId, actionId) {
        const notification = this.notifications.get(notificationId);
        if (!notification) return;

        // إرسال حدث مخصص
        const event = new CustomEvent('notificationAction', {
            detail: { notificationId, actionId }
        });
        document.dispatchEvent(event);

        // إخفاء الإشعار
        this.hideNotification(notificationId);
    }

    /**
     * عرض نافذة حوار تأكيد
     */
    showConfirm(options) {
        return new Promise((resolve) => {
            const {
                type = 'confirm',
                title = 'تأكيد',
                message = 'هل أنت متأكد؟',
                confirmText = 'تأكيد',
                cancelText = 'إلغاء',
                danger = false
            } = options;

            // التحقق من إمكانية عرض نوافذ التأكيد
            if (!this.canShowNotification('confirm')) {
                // إذا كانت نوافذ التأكيد معطلة، نعيد false مباشرة
                resolve(false);
                return;
            }

            const overlay = this.createModalOverlay(type, title, message, [
                {
                    text: cancelText,
                    action: () => {
                        this.hideModal(overlay);
                        resolve(false);
                    }
                },
                {
                    text: confirmText,
                    primary: true,
                    danger: danger,
                    action: () => {
                        this.hideModal(overlay);
                        resolve(true);
                    }
                }
            ]);

            document.body.appendChild(overlay);
            requestAnimationFrame(() => {
                overlay.classList.add('show');
            });
        });
    }

    /**
     * عرض نافذة حوار معلومات
     */
    showAlert(options) {
        return new Promise((resolve) => {
            const {
                type = 'info',
                title = 'معلومات',
                message = '',
                buttonText = 'موافق'
            } = options;

            // التحقق من إمكانية عرض هذا النوع من الإشعارات
            if (!this.canShowNotification(type)) {
                // إذا كان النوع معطلاً، نحل الوعد مباشرة
                resolve();
                return;
            }

            const overlay = this.createModalOverlay(type, title, message, [
                {
                    text: buttonText,
                    primary: true,
                    action: () => {
                        this.hideModal(overlay);
                        resolve();
                    }
                }
            ]);

            document.body.appendChild(overlay);
            requestAnimationFrame(() => {
                overlay.classList.add('show');
            });
        });
    }

    /**
     * إنشاء نافذة حوار
     */
    createModalOverlay(type, title, message, buttons) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';

        const icons = {
            confirm: '⚠',
            success: '✓',
            error: '✕',
            info: 'ℹ'
        };

        const modalEmojis = {
            confirm: '❓',
            success: '🎉',
            error: '❌',
            info: 'ℹ️'
        };

        const buttonsHTML = buttons.map(button => {
            const className = `modal-btn ${button.primary ? 'primary' : ''} ${button.danger ? 'danger' : ''}`;
            return `<button class="${className}" data-action="${button.text}">${button.text}</button>`;
        }).join('');

        overlay.innerHTML = `
            <div class="modal ${type}">
                <div class="modal-header">
                    <div class="modal-title">
                        <div class="modal-icon">${icons[type]}</div>
                        ${modalEmojis[type]} ${title}
                    </div>
                    <button class="modal-close">×</button>
                </div>
                <div class="modal-content">${message}</div>
                <div class="modal-actions">${buttonsHTML}</div>
            </div>
        `;

        // إضافة مستمعي الأحداث
        const modal = overlay.querySelector('.modal');
        const closeBtn = overlay.querySelector('.modal-close');
        const actionBtns = overlay.querySelectorAll('.modal-btn');

        closeBtn.addEventListener('click', () => {
            const cancelButton = buttons.find(b => !b.primary);
            if (cancelButton && cancelButton.action) {
                cancelButton.action();
            } else {
                this.hideModal(overlay);
            }
        });

        actionBtns.forEach((btn, index) => {
            btn.addEventListener('click', () => {
                if (buttons[index] && buttons[index].action) {
                    buttons[index].action();
                }
            });
        });

        // إغلاق عند النقر خارج النافذة
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                const cancelButton = buttons.find(b => !b.primary);
                if (cancelButton && cancelButton.action) {
                    cancelButton.action();
                } else {
                    this.hideModal(overlay);
                }
            }
        });

        // إغلاق بمفتاح Escape
        const keyHandler = (e) => {
            if (e.key === 'Escape') {
                const cancelButton = buttons.find(b => !b.primary);
                if (cancelButton && cancelButton.action) {
                    cancelButton.action();
                } else {
                    this.hideModal(overlay);
                }
            }
        };
        document.addEventListener('keydown', keyHandler);
        overlay._keyHandler = keyHandler;

        return overlay;
    }

    /**
     * إخفاء نافذة الحوار
     */
    hideModal(overlay) {
        overlay.classList.remove('show');

        // إزالة مستمع أحداث لوحة المفاتيح
        if (overlay._keyHandler) {
            document.removeEventListener('keydown', overlay._keyHandler);
        }

        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }

    /**
     * مسح جميع الإشعارات
     */
    clearAll() {
        this.notifications.forEach((notification, id) => {
            this.hideNotification(id);
        });
    }

    /**
     * إنشاء نافذة حوار
     */
    createModalOverlay(type, title, message, buttons) {
        const overlay = document.createElement('div');
        overlay.className = 'modal-overlay';

        const icons = {
            confirm: '⚠',
            success: '✓',
            error: '✕',
            info: 'ℹ'
        };

        const modalEmojis = {
            confirm: '❓',
            success: '🎉',
            error: '❌',
            info: 'ℹ️'
        };

        const buttonsHTML = buttons.map(button => {
            const className = `modal-btn ${button.primary ? 'primary' : ''} ${button.danger ? 'danger' : ''}`;
            return `<button class="${className}" data-action="${button.text}">${button.text}</button>`;
        }).join('');

        overlay.innerHTML = `
            <div class="modal ${type}">
                <div class="modal-header">
                    <div class="modal-title">
                        <div class="modal-icon">${icons[type]}</div>
                        ${modalEmojis[type]} ${title}
                    </div>
                    <button class="modal-close">×</button>
                </div>
                <div class="modal-content">${message}</div>
                <div class="modal-actions">${buttonsHTML}</div>
            </div>
        `;

        // إضافة مستمعي الأحداث
        const modal = overlay.querySelector('.modal');
        const closeBtn = overlay.querySelector('.modal-close');
        const actionBtns = overlay.querySelectorAll('.modal-btn');

        closeBtn.addEventListener('click', () => {
            const cancelButton = buttons.find(b => !b.primary);
            if (cancelButton && cancelButton.action) {
                cancelButton.action();
            } else {
                this.hideModal(overlay);
            }
        });

        actionBtns.forEach((btn, index) => {
            btn.addEventListener('click', () => {
                if (buttons[index] && buttons[index].action) {
                    buttons[index].action();
                }
            });
        });

        // إغلاق عند النقر خارج النافذة
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                const cancelButton = buttons.find(b => !b.primary);
                if (cancelButton && cancelButton.action) {
                    cancelButton.action();
                } else {
                    this.hideModal(overlay);
                }
            }
        });

        // دعم اختصارات لوحة المفاتيح
        const keyHandler = (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                const cancelButton = buttons.find(b => !b.primary);
                if (cancelButton && cancelButton.action) {
                    cancelButton.action();
                } else {
                    this.hideModal(overlay);
                }
            } else if (e.key === 'Enter') {
                e.preventDefault();
                const primaryButton = buttons.find(b => b.primary);
                if (primaryButton && primaryButton.action) {
                    primaryButton.action();
                }
            }
        };

        document.addEventListener('keydown', keyHandler);

        // إزالة مستمع الأحداث عند إغلاق النافذة
        overlay._keyHandler = keyHandler;

        return overlay;
    }

    /**
     * إخفاء نافذة الحوار
     */
    hideModal(overlay) {
        overlay.classList.remove('show');

        // إزالة مستمع أحداث لوحة المفاتيح
        if (overlay._keyHandler) {
            document.removeEventListener('keydown', overlay._keyHandler);
        }

        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }

    /**
     * مسح جميع الإشعارات
     */
    clearAll() {
        this.notifications.forEach((notification, id) => {
            this.hideNotification(id);
        });
    }
}

// إنشاء مثيل عام للنظام
const notificationSystem = new NotificationSystem();

// دوال مساعدة سريعة (بدون showSuccess)
window.showError = (message, title) => notificationSystem.showNotification({
    type: 'error',
    title: title || 'خطأ',
    message: message
});

window.showWarning = (message, title) => notificationSystem.showNotification({
    type: 'warning',
    title: title || 'تحذير',
    message: message
});

window.showInfo = (message, title) => notificationSystem.showNotification({
    type: 'info',
    title: title || 'معلومات',
    message: message
});

window.showConfirm = (message, title) => notificationSystem.showConfirm({
    message: message,
    title: title || 'تأكيد'
});

window.showAlert = (message, title, type) => notificationSystem.showAlert({
    message: message,
    title: title || 'تنبيه',
    type: type || 'info'
});

// إضافة باقي الدوال المطلوبة
window.showProgress = (message, title) => notificationSystem.showNotification({
    type: 'info',
    title: title || 'جاري المعالجة...',
    message: message,
    duration: 0, // لا يختفي تلقائياً
    progress: true
});

window.showNotificationWithActions = (message, title, actions, type = 'info') => {
    return notificationSystem.showNotification({
        type: type,
        title: title,
        message: message,
        actions: actions,
        duration: 0 // لا يختفي تلقائياً مع الأزرار
    });
};

window.setNotificationPosition = (position) => {
    notificationSystem.setPosition(position);
};

// دوال إدارة إعدادات الإشعارات
window.disableAllNotifications = () => {
    notificationSystem.updateSettings({ enabled: false });
    console.log('تم تعطيل جميع الإشعارات');
};

window.enableAllNotifications = () => {
    notificationSystem.updateSettings({ enabled: true });
    console.log('تم تفعيل جميع الإشعارات');
};

window.disableNotificationType = (type) => {
    const types = { ...notificationSystem.settings.types };
    types[type] = false;
    notificationSystem.updateSettings({ types });
    console.log(`تم تعطيل إشعارات ${type}`);
};

window.enableNotificationType = (type) => {
    const types = { ...notificationSystem.settings.types };
    types[type] = true;
    notificationSystem.updateSettings({ types });
    console.log(`تم تفعيل إشعارات ${type}`);
};

window.disableNotificationSound = () => {
    notificationSystem.updateSettings({ sound: false });
    console.log('تم تعطيل أصوات الإشعارات');
};

window.enableNotificationSound = () => {
    notificationSystem.updateSettings({ sound: true });
    console.log('تم تفعيل أصوات الإشعارات');
};

window.getNotificationSettings = () => {
    return notificationSystem.settings;
};

window.resetNotificationSettings = () => {
    localStorage.removeItem('notificationSettings');
    notificationSystem.settings = notificationSystem.loadSettings();
    console.log('تم إعادة تعيين إعدادات الإشعارات للافتراضية');
};

// دالة لإظهار إشعار تقدم
window.showProgress = (message, title) => notificationSystem.showNotification({
    type: 'info',
    title: title || 'جاري المعالجة...',
    message: message,
    duration: 0, // لا يختفي تلقائياً
    progress: true
});

// دالة لإظهار إشعار مع أزرار مخصصة
window.showNotificationWithActions = (message, title, actions, type = 'info') => {
    return notificationSystem.showNotification({
        type: type,
        title: title,
        message: message,
        actions: actions,
        duration: 0 // لا يختفي تلقائياً مع الأزرار
    });
};

// دالة لتغيير موضع الإشعارات
window.setNotificationPosition = (position) => {
    notificationSystem.setPosition(position);
};

// دوال إدارة إعدادات الإشعارات
window.disableAllNotifications = () => {
    notificationSystem.updateSettings({ enabled: false });
    console.log('تم تعطيل جميع الإشعارات');
};

window.enableAllNotifications = () => {
    notificationSystem.updateSettings({ enabled: true });
    console.log('تم تفعيل جميع الإشعارات');
};

window.disableNotificationType = (type) => {
    const types = { ...notificationSystem.settings.types };
    types[type] = false;
    notificationSystem.updateSettings({ types });
    console.log(`تم تعطيل إشعارات ${type}`);
};

window.enableNotificationType = (type) => {
    const types = { ...notificationSystem.settings.types };
    types[type] = true;
    notificationSystem.updateSettings({ types });
    console.log(`تم تفعيل إشعارات ${type}`);
};

window.disableNotificationSound = () => {
    notificationSystem.updateSettings({ sound: false });
    console.log('تم تعطيل أصوات الإشعارات');
};

window.enableNotificationSound = () => {
    notificationSystem.updateSettings({ sound: true });
    console.log('تم تفعيل أصوات الإشعارات');
};

window.getNotificationSettings = () => {
    return notificationSystem.settings;
};

window.resetNotificationSettings = () => {
    localStorage.removeItem('notificationSettings');
    notificationSystem.settings = notificationSystem.loadSettings();
    console.log('تم إعادة تعيين إعدادات الإشعارات للافتراضية');
};

// دالة لعرض لوحة تحكم الإشعارات محسنة
window.showNotificationSettings = () => {
    const settings = notificationSystem.settings;

    const settingsHTML = `
        <div class="notification-settings-panel">
            <div class="settings-section">
                <div class="setting-item">
                    <div class="setting-toggle">
                        <input type="checkbox" id="toggle-all" ${settings.enabled ? 'checked' : ''} onchange="toggleAllNotifications(this.checked)">
                        <label for="toggle-all" class="toggle-label">
                            <span class="toggle-switch"></span>
                            <span class="setting-text">تفعيل جميع الإشعارات</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <h4 class="section-title">أنواع الإشعارات</h4>
                <div class="settings-grid">
                    <div class="setting-item">
                        <div class="setting-toggle">
                            <input type="checkbox" id="toggle-error" ${settings.types.error ? 'checked' : ''} onchange="toggleNotificationType('error', this.checked)">
                            <label for="toggle-error" class="toggle-label">
                                <span class="toggle-switch"></span>
                                <span class="setting-icon error-icon">❌</span>
                                <span class="setting-text">إشعارات الخطأ</span>
                            </label>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-toggle">
                            <input type="checkbox" id="toggle-warning" ${settings.types.warning ? 'checked' : ''} onchange="toggleNotificationType('warning', this.checked)">
                            <label for="toggle-warning" class="toggle-label">
                                <span class="toggle-switch"></span>
                                <span class="setting-icon warning-icon">⚠️</span>
                                <span class="setting-text">إشعارات التحذير</span>
                            </label>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-toggle">
                            <input type="checkbox" id="toggle-info" ${settings.types.info ? 'checked' : ''} onchange="toggleNotificationType('info', this.checked)">
                            <label for="toggle-info" class="toggle-label">
                                <span class="toggle-switch"></span>
                                <span class="setting-icon info-icon">ℹ️</span>
                                <span class="setting-text">إشعارات المعلومات</span>
                            </label>
                        </div>
                    </div>

                    <div class="setting-item">
                        <div class="setting-toggle">
                            <input type="checkbox" id="toggle-confirm" ${settings.types.confirm ? 'checked' : ''} onchange="toggleNotificationType('confirm', this.checked)">
                            <label for="toggle-confirm" class="toggle-label">
                                <span class="toggle-switch"></span>
                                <span class="setting-icon confirm-icon">❓</span>
                                <span class="setting-text">نوافذ التأكيد</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="settings-section">
                <div class="setting-item">
                    <div class="setting-toggle">
                        <input type="checkbox" id="toggle-sound" ${settings.sound ? 'checked' : ''} onchange="toggleNotificationSound(this.checked)">
                        <label for="toggle-sound" class="toggle-label">
                            <span class="toggle-switch"></span>
                            <span class="setting-icon sound-icon">🔊</span>
                            <span class="setting-text">تفعيل أصوات الإشعارات</span>
                        </label>
                    </div>
                </div>
            </div>

            <div class="settings-actions">
                <button class="reset-btn" onclick="resetNotificationSettings(); location.reload();">
                    <i class="fas fa-undo"></i>
                    إعادة تعيين الإعدادات
                </button>
            </div>
        </div>
    `;

    notificationSystem.showAlert({
        title: 'إعدادات الإشعارات',
        message: settingsHTML,
        buttonText: 'إغلاق'
    });
};

// دوال مساعدة للوحة التحكم
window.toggleAllNotifications = (enabled) => {
    if (enabled) {
        enableAllNotifications();
    } else {
        disableAllNotifications();
    }
};

window.toggleNotificationType = (type, enabled) => {
    if (enabled) {
        enableNotificationType(type);
    } else {
        disableNotificationType(type);
    }
};

window.toggleNotificationSound = (enabled) => {
    if (enabled) {
        enableNotificationSound();
    } else {
        disableNotificationSound();
    }
};



// دالة للحصول على المواضع المتاحة
window.getNotificationPositions = () => {
    return ['top-right', 'top-left', 'bottom-right', 'bottom-left', 'top-center', 'bottom-center'];
};
